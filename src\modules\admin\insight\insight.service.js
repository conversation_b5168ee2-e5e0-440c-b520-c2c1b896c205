/**
 * Admin Insight Service
 *
 * Handles business logic for admin insights
 */
const insightRepository = require('@models/repositories/insight.repository');
const pdCategoryRepository = require('@models/repositories/pd-category.repository');
const focusRepository = require('@models/repositories/focus.repository');
const wtdCategoryRepository = require('@models/repositories/wtd-category.repository');
const insightReportRepository = require('@models/repositories/insight-report.repository');
const contributionReportRepository = require('@models/repositories/contribution-report.repository');
const contributionRepository = require('@models/repositories/contribution.repository');
const { ApiException } = require('@utils/exception.utils');
const { INSIGHT } = require('@utils/messages.utils');
const { InsightStatus } = require('@utils/enums.utils');

/**
 * Admin insight service
 */
const insightService = {
  /**
   * Get all insights with pagination and optional search for admin
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @param {string} options.search - Search term
   * @param {string} options.status - Filter by status
   * @returns {Promise<Object>} Insights and pagination info
   */
  getAllInsights: async ({ page, limit, search, status }) => {
    try {
      return await insightRepository.findAllForAdmin({
        page,
        limit,
        search,
        status,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get insight by ID
   * @param {string} id - Insight ID
   * @returns {Promise<Object>} Insight data
   */
  getInsightById: async (id) => {
    try {
      const insight = await insightRepository.findById(id);

      const result = insight.toJSON();
      delete result.pdCategoryId;
      delete result.createdBy;
      delete result.reviewedBy;

      return result;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update an insight
   * @param {string} id - Insight ID
   * @param {Object} data - Updated data
   * @returns {Promise<Object>} Updated insight
   */
  updateInsight: async (id, data) => {
    try {
      // Validate PD category exists if provided
      if (data.pdCategoryId) {
        await pdCategoryRepository.findById(data.pdCategoryId);
      }

      // Validate focus IDs if provided
      if (data.focusIds && data.focusIds.length > 0) {
        for (const focusId of data.focusIds) {
          await focusRepository.findById(focusId);
        }
      }

      // Validate WTD category IDs if provided
      if (data.wtdCategoryIds && data.wtdCategoryIds.length > 0) {
        for (const categoryId of data.wtdCategoryIds) {
          await wtdCategoryRepository.findById(categoryId);
        }
      }

      // Update insight
      return await insightRepository.update(id, data);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Delete an insight
   * @param {string} id - Insight ID
   * @returns {Promise<boolean>} True if deleted
   */
  deleteInsight: async (id) => {
    try {
      return await insightRepository.delete(id);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update insight status (approve or reject)
   * @param {string} id - Insight ID
   * @param {string} adminId - Admin ID
   * @param {string} status - New status (APPROVED or REJECTED)
   * @param {string} rejectionReason - Reason for rejection (required if status is REJECTED)
   * @returns {Promise<Object>} Updated insight
   */
  updateStatus: async (id, adminId, status, rejectionReason) => {
    try {
      const insight = await insightRepository.findById(id);

      // Check if insight is already reviewed
      if (insight.status !== InsightStatus.PENDING) {
        throw new ApiException(400, INSIGHT.ALREADY_REVIEWED);
      }

      // Validate status
      if (!InsightStatus.values.includes(status)) {
        throw new ApiException(400, 'Invalid status value');
      }

      // If rejecting, require a reason
      if (status === InsightStatus.REJECTED && !rejectionReason) {
        throw new ApiException(400, 'Rejection reason is required');
      }

      // Update insight status
      const updateData = {
        status: status,
        reviewedBy: adminId,
        reviewedAt: new Date(),
        rejectionReason:
          status === InsightStatus.REJECTED ? rejectionReason : null,
      };

      return await insightRepository.update(id, updateData);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all insight reports with pagination and optional search
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @param {string} options.search - Search term
   * @returns {Promise<Object>} Reports and pagination info
   */
  getInsightReports: async ({ page, limit, search }) => {
    try {
      const result = await insightReportRepository.findAll({
        page,
        limit,
        search,
      });

      // Remove sensitive fields from reports
      const reportsWithoutSensitiveData = result.reports.map((report) => {
        const reportData = report.toJSON ? report.toJSON() : report;

        // Remove reportedBy field for privacy
        delete reportData.reportedBy;

        return reportData;
      });

      return {
        reports: reportsWithoutSensitiveData,
        pagination: result.pagination,
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all contribution reports with pagination and optional search
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @param {string} options.search - Search term
   * @returns {Promise<Object>} Reports and pagination info
   */
  getContributionReports: async ({ page, limit, search }) => {
    try {
      const result = await contributionReportRepository.findAll({
        page,
        limit,
        search,
      });

      // Remove sensitive fields from reports
      const reportsWithoutSensitiveData = result.reports.map((report) => {
        const reportData = report.toJSON ? report.toJSON() : report;

        // Remove reportedBy field for privacy
        delete reportData.reportedBy;

        return reportData;
      });

      return {
        reports: reportsWithoutSensitiveData,
        pagination: result.pagination,
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Delete a contribution (admin only)
   * @param {string} contributionId - Contribution ID
   * @returns {Promise<boolean>} True if deleted
   */
  deleteContribution: async (contributionId) => {
    try {
      // Check if contribution exists
      const contribution = await contributionRepository.findById(
        contributionId,
        false
      );
      if (!contribution) {
        throw new ApiException(404, 'Contribution not found');
      }

      // Delete the contribution
      return await contributionRepository.delete(contributionId);
    } catch (error) {
      throw error;
    }
  },
};

module.exports = insightService;
