/**
 * Experience Service
 *
 * Business logic for experience operations
 */
const experienceRepository = require('@models/repositories/experience.repository');
const { ApiException } = require('@utils/exception.utils');

/**
 * Experience service
 */
const experienceService = {
  /**
   * Create a new experience with weeks, insights, and media
   * @param {Object} data - Experience data
   * @param {string} userId - Creator user ID
   * @returns {Promise<Object>} Created experience
   */
  createExperience: async (data, userId) => {
    try {
      const experienceData = {
        ...data,
        createdBy: userId,
      };

      return await experienceRepository.create(experienceData);
    } catch (error) {
      console.error('Error in createExperience service:', error);
      throw error;
    }
  },

  /**
   * Get all experiences with pagination and search
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Experiences with pagination
   */
  getAllExperiences: async (options) => {
    try {
      const { page = 1, limit = 10, search = '', userId } = options;

      const queryOptions = {
        page,
        limit,
        search,
      };

      if (userId) {
        queryOptions.createdBy = userId;
      }

      return await experienceRepository.findAll(queryOptions);
    } catch (error) {
      console.error('Error in getAllExperiences service:', error);
      throw error;
    }
  },

  /**
   * Get experience by ID with all associations
   * @param {string} id - Experience ID
   * @returns {Promise<Object>} Experience with associations
   */
  getExperienceById: async (id) => {
    try {
      const experience = await experienceRepository.findById(id);

      if (!experience) {
        throw new ApiException('Experience not found', 404);
      }

      return experience;
    } catch (error) {
      console.error('Error in getExperienceById service:', error);
      throw error;
    }
  },
};

module.exports = experienceService;
