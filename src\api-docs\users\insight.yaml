openapi: 3.0.0
info:
  title: WTD Platform User Insights API
  version: 1.0.0
  description: API endpoints for managing insights as a user

paths:
  /user/insights:
    post:
      tags:
        - User Insights
      summary: Create New Insight
      description: Create a new insight (Provider Plus users only)
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateInsightRequest'
      responses:
        '201':
          description: Insight created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InsightResponse'
        '400':
          description: Bad Request - Invalid input
        '401':
          description: Unauthorized - User is not authenticated
        '403':
          description: Forbidden - User is not a Provider Plus user

    get:
      tags:
        - User Insights
      summary: List All Insights
      description: Get a list of all insights with pagination, search, and filtering by focus, PD categories, and WTD categories
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SearchParam'
        - $ref: '#/components/parameters/FocusIdsParam'
        - $ref: '#/components/parameters/PdCategoryIdsParam'
        - $ref: '#/components/parameters/WtdCategoryIdsParam'
      responses:
        '200':
          description: Successfully retrieved insights
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InsightListResponse'
        '401':
          description: Unauthorized - User is not authenticated

  /user/insights/trending:
    get:
      tags:
        - User Insights
      summary: Get Trending Insights
      description: Get a list of trending insights based on contributions and likes, with pagination
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
      responses:
        '200':
          description: Successfully retrieved trending insights
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TrendingInsightListResponse'
        '401':
          description: Unauthorized - User is not authenticated

  /user/insights/{id}:
    get:
      tags:
        - User Insights
      summary: Get Insight by ID
      description: Get a specific insight by its ID
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/InsightIdParam'
      responses:
        '200':
          description: Successfully retrieved insight
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InsightResponse'
        '401':
          description: Unauthorized - User is not authenticated
        '404':
          description: Insight not found

  /user/insights/{insightId}/bookmark:
    post:
      tags:
        - User Insights
      summary: Toggle Bookmark Status
      description: Bookmark or unbookmark an insight
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/BookmarkInsightIdParam'
      responses:
        '200':
          description: Successfully toggled bookmark status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookmarkToggleResponse'
        '401':
          description: Unauthorized - User is not authenticated
        '404':
          description: Insight not found

  /user/insights/bookmarks:
    get:
      tags:
        - User Insights
      summary: Get All Bookmarked Insights
      description: Get a list of all insights bookmarked by the current user
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
      responses:
        '200':
          description: Successfully retrieved bookmarked insights
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookmarkedInsightsResponse'
        '401':
          description: Unauthorized - User is not authenticated

  /user/insights/{insightId}/like:
    post:
      tags:
        - User Insights
      summary: Toggle Like Status
      description: Like or unlike an insight
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/LikeInsightIdParam'
      responses:
        '200':
          description: Successfully toggled like status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LikeToggleResponse'
        '401':
          description: Unauthorized - User is not authenticated
        '404':
          description: Insight not found

  /user/insights/{insightId}/implement:
    post:
      tags:
        - User Insights
      summary: Toggle Implement Status
      description: Mark or unmark an insight as implemented
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ImplementInsightIdParam'
      responses:
        '200':
          description: Successfully toggled implement status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImplementToggleResponse'
        '401':
          description: Unauthorized - User is not authenticated
        '404':
          description: Insight not found

  /user/insights/{insightId}/contribution:
    post:
      tags:
        - User Insights - Contributions
      summary: Add Contribution (Comment)
      description: Add a comment/contribution to an insight
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ContributionInsightIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateContributionRequest'
      responses:
        '201':
          description: Contribution added successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContributionResponse'
        '400':
          description: Bad Request - Invalid input
        '401':
          description: Unauthorized - User is not authenticated
        '404':
          description: Insight not found

    get:
      tags:
        - User Insights - Contributions
      summary: Get Contributions for Insight
      description: Get all contributions (comments) for a specific insight with pagination
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ContributionInsightIdParam'
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SortByParam'
        - $ref: '#/components/parameters/SortOrderParam'
      responses:
        '200':
          description: Successfully retrieved contributions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContributionListResponse'
        '401':
          description: Unauthorized - User is not authenticated
        '404':
          description: Insight not found

  /user/insights/contribution/{contributionId}:
    post:
      tags:
        - User Insights - Contributions
      summary: Toggle Contribution Like
      description: Like or unlike a contribution (comment)
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ContributionIdParam'
      responses:
        '200':
          description: Successfully toggled contribution like status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContributionLikeToggleResponse'
        '401':
          description: Unauthorized - User is not authenticated
        '404':
          description: Contribution not found

    delete:
      tags:
        - User Insights - Contributions
      summary: Delete Contribution
      description: Delete a contribution (user can only delete their own contributions)
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ContributionIdParam'
      responses:
        '200':
          description: Contribution deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "Contribution deleted successfully"
        '401':
          description: Unauthorized - User is not authenticated
        '403':
          description: Forbidden - User can only delete their own contributions
        '404':
          description: Contribution not found

  /user/insights/{insightId}/report:
    post:
      tags:
        - User Insights - Reports
      summary: Report an Insight
      description: Report an insight for inappropriate content or other violations
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ReportInsightIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateReportRequest'
      responses:
        '201':
          description: Insight reported successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportResponse'
        '400':
          description: Bad request - Already reported or cannot report own content
        '401':
          description: Unauthorized - User is not authenticated
        '404':
          description: Insight not found

  /user/insights/contribution/{contributionId}/report:
    post:
      tags:
        - User Insights - Reports
      summary: Report a Contribution
      description: Report a contribution for inappropriate content or other violations
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ReportContributionIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateReportRequest'
      responses:
        '201':
          description: Contribution reported successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContributionReportResponse'
        '400':
          description: Bad request - Already reported or cannot report own content
        '401':
          description: Unauthorized - User is not authenticated
        '404':
          description: Contribution not found

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    PageParam:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number (1-based)

    LimitParam:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
      description: Number of items per page

    SearchParam:
      name: search
      in: query
      schema:
        type: string
      description: Optional search term to filter insights by text

    InsightIdParam:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Insight ID

    FocusIdsParam:
      name: focusIds
      in: query
      style: form
      explode: true
      schema:
        type: array
        items:
          type: string
          format: uuid
      description: Array of focus UUIDs to filter by

    PdCategoryIdsParam:
      name: pdCategoryIds
      in: query
      style: form
      explode: true
      schema:
        type: array
        items:
          type: string
          format: uuid
      description: Array of PD category UUIDs to filter by

    WtdCategoryIdsParam:
      name: wtdCategoryIds
      in: query
      style: form
      explode: true
      schema:
        type: array
        items:
          type: string
          format: uuid
      description: Array of WTD category UUIDs to filter by

    BookmarkInsightIdParam:
      name: insightId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Insight ID to bookmark/unbookmark

    LikeInsightIdParam:
      name: insightId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Insight ID to like/unlike

    ImplementInsightIdParam:
      name: insightId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Insight ID to implement/unimplement

    ContributionInsightIdParam:
      name: insightId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Insight ID for contributions

    ContributionIdParam:
      name: contributionId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Contribution ID

    SortByParam:
      name: sortBy
      in: query
      required: false
      schema:
        type: string
        enum: [createdAt, updatedAt]
        default: createdAt
      description: Field to sort contributions by

    SortOrderParam:
      name: sortOrder
      in: query
      required: false
      schema:
        type: string
        enum: [ASC, DESC]
        default: DESC
      description: Sort order for contributions

    ReportInsightIdParam:
      name: insightId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Insight ID to report

    ReportContributionIdParam:
      name: contributionId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Contribution ID to report

  schemas:
    Insight:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the insight
        insightText:
          type: string
          description: The insight text
        sourceUrl:
          type: string
          format: uri
          description: Source URL for the insight
        pdCategory:
          $ref: '#/components/schemas/PdCategoryRef'
        focus:
          type: array
          items:
            $ref: '#/components/schemas/FocusRef'
        wtdCategories:
          type: array
          items:
            $ref: '#/components/schemas/WtdCategoryRef'
        isBookmarked:
          type: boolean
          description: Whether the insight is bookmarked by the current user
        isLiked:
          type: boolean
          description: Whether the insight is liked by the current user
        isImplemented:
          type: boolean
          description: Whether the insight is implemented by the current user
        createdAt:
          type: string
          format: date-time
          description: Date and time when the insight was created
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the insight was last updated
      example:
        id: "123e4567-e89b-12d3-a456-************"
        insightText: "Regular physical activity improves cognitive function and academic performance."
        sourceUrl: "https://example.com/research-paper"
        pdCategory:
          id: "123e4567-e89b-12d3-a456-************"
          name: "Student Well-Being"
        focus:
          - id: "123e4567-e89b-12d3-a456-************"
            name: "Elementary"
          - id: "123e4567-e89b-12d3-a456-************"
            name: "Secondary"
        wtdCategories:
          - id: "123e4567-e89b-12d3-a456-************"
            name: "Movement"
          - id: "123e4567-e89b-12d3-a456-426614174005"
            name: "Mindset"
        isBookmarked: true
        isLiked: false
        isImplemented: false
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"

    TrendingInsight:
      allOf:
        - $ref: '#/components/schemas/Insight'
        - type: object
          properties:
            likesCount:
              type: integer
              description: Number of likes on this insight
              example: 25
            totalContributions:
              type: integer
              description: Number of contributions (comments) on this insight
              example: 12
            implementationCount:
              type: integer
              description: Number of users who have implemented this insight
              example: 8
            bookmarksCount:
              type: integer
              description: Number of users who have bookmarked this insight
              example: 15
            trendingScore:
              type: number
              format: float
              description: Calculated trending score based on likes and contributions only
              example: 37.0
            creator:
              $ref: '#/components/schemas/UserRef'
      example:
        id: "123e4567-e89b-12d3-a456-************"
        insightText: "Regular physical activity improves cognitive function and academic performance."
        sourceUrl: "https://example.com/research-paper"
        pdCategory:
          id: "123e4567-e89b-12d3-a456-************"
          name: "Student Well-Being"
        focus:
          - id: "123e4567-e89b-12d3-a456-************"
            name: "Elementary"
        wtdCategories:
          - id: "123e4567-e89b-12d3-a456-************"
            name: "Movement"
        isBookmarked: true
        isLiked: false
        isImplemented: true
        likesCount: 25
        totalContributions: 12
        implementationCount: 8
        bookmarksCount: 15
        trendingScore: 37.0
        creator:
          id: "123e4567-e89b-12d3-a456-************"
          firstName: "Jane"
          lastName: "Smith"
          profilePic: "https://example.com/profile.jpg"
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"

    PdCategoryRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    FocusRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    WtdCategoryRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string

    CreateInsightRequest:
      type: object
      required:
        - insightText
        - pdCategoryId
      properties:
        insightText:
          type: string
          maxLength: 250
          description: The insight text
          example: "Regular physical activity improves cognitive function and academic performance."
        sourceUrl:
          type: string
          format: uri
          description: Optional source URL
          example: "https://example.com/research-paper"
        pdCategoryId:
          type: string
          format: uuid
          description: PD category ID
          example: "123e4567-e89b-12d3-a456-************"
        focusIds:
          type: array
          description: Optional array of focus IDs
          items:
            type: string
            format: uuid
          example: ["123e4567-e89b-12d3-a456-************", "123e4567-e89b-12d3-a456-************"]
        wtdCategoryIds:
          type: array
          description: Optional array of WTD category IDs
          items:
            type: string
            format: uuid
          example: ["123e4567-e89b-12d3-a456-************", "123e4567-e89b-12d3-a456-************"]

    InsightResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Insight retrieved successfully"
        data:
          $ref: '#/components/schemas/Insight'

    InsightListResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "All insights retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/Insight'
        pagination:
          $ref: '#/components/schemas/Pagination'

    TrendingInsightListResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Trending insights retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/TrendingInsight'
        pagination:
          $ref: '#/components/schemas/Pagination'

    BookmarkToggleResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Insight bookmarked successfully"
        data:
          type: object
          properties:
            isBookmarked:
              type: boolean
              example: true

    ImplementToggleResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Insight marked as implemented successfully"
        data:
          type: object
          properties:
            isImplemented:
              type: boolean
              example: true

    LikeToggleResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Insight liked successfully"
        data:
          type: object
          properties:
            isLiked:
              type: boolean
              example: true

    BookmarkedInsightsResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Bookmarked insights retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/Insight'
        pagination:
          $ref: '#/components/schemas/Pagination'

    LikedInsightsResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Liked insights retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/Insight'
        pagination:
          $ref: '#/components/schemas/Pagination'

    ImplementedInsightsResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Implemented insights retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/Insight'
        pagination:
          $ref: '#/components/schemas/Pagination'

    Pagination:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 3
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        totalPages:
          type: integer
          description: Total number of pages
          example: 1
        hasNext:
          type: boolean
          description: Whether there is a next page
          example: false
        hasPrevious:
          type: boolean
          description: Whether there is a previous page
          example: false

    # Contribution Schemas
    Contribution:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the contribution
        insightId:
          type: string
          format: uuid
          description: ID of the insight this contribution belongs to
        contributedBy:
          type: string
          format: uuid
          description: ID of the user who made the contribution
        text:
          type: string
          description: The contribution text/comment
          maxLength: 1000
        createdAt:
          type: string
          format: date-time
          description: When the contribution was created
        updatedAt:
          type: string
          format: date-time
          description: When the contribution was last updated
        contributor:
          $ref: '#/components/schemas/UserRef'
        likesCount:
          type: integer
          description: Number of likes on this contribution
        isLikedByUser:
          type: boolean
          description: Whether the current user has liked this contribution
      example:
        id: "123e4567-e89b-12d3-a456-************"
        insightId: "123e4567-e89b-12d3-a456-************"
        contributedBy: "123e4567-e89b-12d3-a456-************"
        text: "This insight really helped me improve my teaching methods!"
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"
        contributor:
          id: "123e4567-e89b-12d3-a456-************"
          firstName: "John"
          lastName: "Doe"
          profilePic: "https://example.com/profile.jpg"
        likesCount: 5
        isLikedByUser: true

    UserRef:
      type: object
      properties:
        id:
          type: string
          format: uuid
        firstName:
          type: string
        lastName:
          type: string
        profilePic:
          type: string
          format: uri

    CreateContributionRequest:
      type: object
      required:
        - content
      properties:
        content:
          type: string
          description: The contribution content/comment
          minLength: 1
          maxLength: 1000
          example: "This insight really helped me improve my teaching methods!"

    ContributionResponse:
      type: object
      properties:
        status:
          type: integer
          example: 201
        message:
          type: string
          example: "Contribution added successfully"
        data:
          $ref: '#/components/schemas/Contribution'

    ContributionListResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Contributions retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/Contribution'
        pagination:
          $ref: '#/components/schemas/Pagination'

    ContributionLikeToggleResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Contribution liked successfully"
        data:
          type: object
          properties:
            isLiked:
              type: boolean
              example: true

    CreateReportRequest:
      type: object
      properties:
        reason:
          type: string
          description: Reason for reporting (optional)
          maxLength: 1000
          example: "This content contains inappropriate material"

    ReportResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Insight reported successfully"
        data:
          $ref: '#/components/schemas/Report'

    ContributionReportResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Contribution reported successfully"
        data:
          $ref: '#/components/schemas/ContributionReport'

    Report:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the report
        insightId:
          type: string
          format: uuid
          description: ID of the reported insight
        reportedBy:
          type: string
          format: uuid
          description: ID of the user who reported
        reason:
          type: string
          nullable: true
          description: Reason for reporting
        createdAt:
          type: string
          format: date-time
          description: Date and time when the report was created
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the report was last updated
      example:
        id: "123e4567-e89b-12d3-a456-************"
        insightId: "123e4567-e89b-12d3-a456-************"
        reportedBy: "123e4567-e89b-12d3-a456-************"
        reason: "This content contains inappropriate material"
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"

    ContributionReport:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the report
        contributionId:
          type: string
          format: uuid
          description: ID of the reported contribution
        reportedBy:
          type: string
          format: uuid
          description: ID of the user who reported
        reason:
          type: string
          nullable: true
          description: Reason for reporting
        createdAt:
          type: string
          format: date-time
          description: Date and time when the report was created
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the report was last updated
      example:
        id: "123e4567-e89b-12d3-a456-************"
        contributionId: "123e4567-e89b-12d3-a456-************"
        reportedBy: "123e4567-e89b-12d3-a456-************"
        reason: "This contribution contains offensive language"
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"


