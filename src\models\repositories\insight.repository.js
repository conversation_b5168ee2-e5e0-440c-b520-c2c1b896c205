/**
 * Insight Repository
 *
 * Handles data access operations for the Insight model
 */
const { ApiException } = require('@utils/exception.utils');
const { Op } = require('sequelize');
const databaseService = require('@config/database.config');
const { v4: uuidv4 } = require('uuid');
const { InsightStatus } = require('@utils/enums.utils');

class InsightRepository {
  /**
   * Find all insights with pagination and optional search (for regular users)
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.search - Optional search term for insightText
   * @param {Array<string>} options.focusIds - Optional array of focus IDs to filter by
   * @param {Array<string>} options.pdCategoryIds - Optional array of PD category IDs to filter by
   * @param {Array<string>} options.wtdCategoryIds - Optional array of WTD category IDs to filter by
   * @param {string} options.userId - Optional user ID to check if insights are bookmarked
   * @returns {Promise<Object>} Object containing insights and pagination info
   */
  async findAll({
    page = 1,
    limit = 10,
    search = '',
    focusIds = [],
    pdCategoryIds = [],
    wtdCategoryIds = [],
    userId = null,
  } = {}) {
    try {
      // Build where clause for search
      const whereClause = {
        status: InsightStatus.APPROVED,
      };

      if (search) {
        whereClause.insightText = {
          [Op.iLike]: `%${search}%`,
        };
      }

      // Filter by PD categories if provided
      if (pdCategoryIds && pdCategoryIds.length > 0) {
        // Get the Insight model to find insights with the specified PD categories
        const Insight = databaseService.getInsightModel();

        // Find insight IDs that have the specified PD category IDs
        const insightsWithPdCategory = await Insight.findAll({
          attributes: ['id'],
          where: {
            pdCategoryId: {
              [Op.in]: pdCategoryIds,
            },
            status: InsightStatus.APPROVED, // Maintain the status filter
          },
          raw: true,
        });

        // Extract the insight IDs
        const insightIds = insightsWithPdCategory.map((item) => item.id);

        // Add to the where clause
        if (insightIds.length > 0) {
          // If we already have an ID filter, we need to find the intersection
          if (whereClause.id) {
            const existingIds = whereClause.id[Op.in];
            const intersectionIds = existingIds.filter((id) =>
              insightIds.includes(id)
            );

            if (intersectionIds.length > 0) {
              whereClause.id = {
                [Op.in]: intersectionIds,
              };
            } else {
              // If no insights match both filters, return empty result
              whereClause.id = null;
            }
          } else {
            whereClause.id = {
              [Op.in]: insightIds,
            };
          }
        } else {
          // If no insights match the PD category filter, return empty result
          whereClause.id = null;
        }

        // Remove the pdCategoryId filter since we're now filtering by ID
        delete whereClause.pdCategoryId;
      }

      // Calculate offset
      const offset = (page - 1) * limit;

      // Get models
      const User = databaseService.getUserModel();
      const PdCategory = databaseService.getPdCategoryModel();
      const Focus = databaseService.getFocusModel();
      const WtdCategory = databaseService.getWtdCategoryModel();

      // Prepare include array for associations
      const includeArray = [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'profilePic'],
        },
        {
          model: PdCategory,
          as: 'pdCategory',
          attributes: ['id', 'name'],
        },
        {
          model: Focus,
          as: 'focus',
          attributes: ['id', 'name'],
          through: { attributes: [] }, // Exclude junction table attributes
        },
        {
          model: WtdCategory,
          as: 'wtdCategories',
          attributes: ['id', 'name'],
          through: { attributes: [] }, // Exclude junction table attributes
        },
      ];

      // Create a separate where clause for filtering by associations
      // This approach allows us to filter insights but still return all associated data

      // Filter by focus if provided
      if (focusIds && focusIds.length > 0) {
        // Get the InsightFocus model
        const InsightFocus = databaseService.getInsightFocusModel();

        // Find insight IDs that have the specified focus IDs
        const insightIdsWithFocus = await InsightFocus.findAll({
          attributes: ['insightId'],
          where: {
            focusId: {
              [Op.in]: focusIds,
            },
          },
          raw: true,
        });

        // Extract the insight IDs
        const insightIds = insightIdsWithFocus.map((item) => item.insightId);

        // Add to the where clause
        if (insightIds.length > 0) {
          whereClause.id = {
            [Op.in]: insightIds,
          };
        } else {
          // If no insights match the focus filter, return empty result
          whereClause.id = null;
        }
      }

      // Filter by WTD categories if provided
      if (wtdCategoryIds && wtdCategoryIds.length > 0) {
        // Get the InsightWtdCategory model
        const InsightWtdCategory = databaseService.getInsightWtdCategoryModel();

        // Find insight IDs that have the specified WTD category IDs
        const insightIdsWithWtdCategory = await InsightWtdCategory.findAll({
          attributes: ['insightId'],
          where: {
            wtdCategoryId: {
              [Op.in]: wtdCategoryIds,
            },
          },
          raw: true,
        });

        // Extract the insight IDs
        const insightIds = insightIdsWithWtdCategory.map(
          (item) => item.insightId
        );

        // Add to the where clause
        if (insightIds.length > 0) {
          // If we already have an ID filter from focus, we need to find the intersection
          if (whereClause.id) {
            const existingIds = whereClause.id[Op.in];
            const intersectionIds = existingIds.filter((id) =>
              insightIds.includes(id)
            );

            if (intersectionIds.length > 0) {
              whereClause.id = {
                [Op.in]: intersectionIds,
              };
            } else {
              // If no insights match both filters, return empty result
              whereClause.id = null;
            }
          } else {
            whereClause.id = {
              [Op.in]: insightIds,
            };
          }
        } else if (wtdCategoryIds.length > 0) {
          // If no insights match the WTD category filter, return empty result
          whereClause.id = null;
        }
      }

      // Find insights with pagination
      const Insight = databaseService.getInsightModel();
      const { count, rows } = await Insight.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        distinct: true,
        order: [['createdAt', 'DESC']],
        attributes: {
          exclude: [
            'pdCategoryId',
            'createdBy',
            'status',
            'reviewedBy',
            'reviewedAt',
            'rejectionReason',
          ],
          include: [
            // Add subquery for likes count
            [
              databaseService
                .getSequelize()
                .literal(
                  `(SELECT COUNT(*)::INTEGER FROM "LikedInsight" WHERE "LikedInsight"."insightId" = "Insight"."id")`
                ),
              'likesCount',
            ],
            // Add subquery for implementations count
            [
              databaseService
                .getSequelize()
                .literal(
                  `(SELECT COUNT(*)::INTEGER FROM "ImplementedInsight" WHERE "ImplementedInsight"."insightId" = "Insight"."id")`
                ),
              'implementationCount',
            ],
            // Add subquery for contributions count
            [
              databaseService
                .getSequelize()
                .literal(
                  `(SELECT COUNT(*)::INTEGER FROM "Contribution" WHERE "Contribution"."insightId" = "Insight"."id")`
                ),
              'totalContributions',
            ],
            // Add subquery for bookmarks count
            [
              databaseService
                .getSequelize()
                .literal(
                  `(SELECT COUNT(*)::INTEGER FROM "BookmarkedInsight" WHERE "BookmarkedInsight"."insightId" = "Insight"."id")`
                ),
              'bookmarksCount',
            ],
          ],
        },
        include: includeArray,
        subQuery: false, // Important for correct count with filtering by associations
      });

      // Calculate pagination info
      const totalPages = Math.ceil(count / limit);
      const hasNext = page < totalPages;
      const hasPrevious = page > 1;

      // If userId is provided, check which insights are bookmarked, liked, and implemented
      let insights = rows;
      if (userId) {
        const BookmarkedInsight = databaseService.getBookmarkedInsightModel();
        const LikedInsight = databaseService.getLikedInsightModel();
        const ImplementedInsight = databaseService.getImplementedInsightModel();

        // Get all bookmarks for this user and the returned insights
        const insightIds = rows.map((insight) => insight.id);
        const bookmarks = await BookmarkedInsight.findAll({
          where: {
            userId,
            insightId: {
              [Op.in]: insightIds,
            },
          },
          raw: true,
        });

        // Get all likes for this user and the returned insights
        const likes = await LikedInsight.findAll({
          where: {
            userId,
            insightId: {
              [Op.in]: insightIds,
            },
          },
          raw: true,
        });

        // Get all implementations for this user and the returned insights
        const implementations = await ImplementedInsight.findAll({
          where: {
            userId,
            insightId: {
              [Op.in]: insightIds,
            },
          },
          raw: true,
        });

        // Create sets for faster lookup
        const bookmarkedInsightIds = new Set(bookmarks.map((b) => b.insightId));
        const likedInsightIds = new Set(likes.map((l) => l.insightId));
        const implementedInsightIds = new Set(
          implementations.map((i) => i.insightId)
        );

        // Add isBookmarked, isLiked, and isImplemented flags to each insight
        insights = rows.map((insight) => {
          const insightData = insight.toJSON();
          insightData.isBookmarked = bookmarkedInsightIds.has(insight.id);
          insightData.isLiked = likedInsightIds.has(insight.id);
          insightData.isImplemented = implementedInsightIds.has(insight.id);
          return insightData;
        });
      }

      return {
        insights,
        pagination: {
          total: count,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrevious,
        },
      };
    } catch (error) {
      console.error('Error in findAll repository:', error);
      throw error;
    }
  }

  /**
   * Find all insights with pagination and optional search (for admin users)
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.search - Optional search term for insightText
   * @param {string} options.status - Optional filter by status
   * @returns {Promise<Object>} Object containing insights and pagination info
   */
  async findAllForAdmin({
    page = 1,
    limit = 10,
    search = '',
    status = null,
  } = {}) {
    try {
      const Insight = databaseService.getInsightModel();
      const PdCategory = databaseService.getPdCategoryModel();
      const Focus = databaseService.getFocusModel();
      const WtdCategory = databaseService.getWtdCategoryModel();
      const User = databaseService.getUserModel();
      const Admin = databaseService.getAdminModel();

      // Build where clause for search and status
      const whereClause = {};
      if (search) {
        whereClause.insightText = {
          [Op.iLike]: `%${search}%`,
        };
      }

      // Filter by status if provided
      if (status) {
        whereClause.status = status;
      }

      // Calculate offset for pagination
      const offset = (page - 1) * limit;

      // Find insights with pagination
      const { count, rows } = await Insight.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        order: [['createdAt', 'DESC']],
        attributes: {
          exclude: ['pdCategoryId', 'createdBy', 'reviewedBy'],
        },
        include: [
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'firstName', 'lastName', 'profilePic'],
          },
          {
            model: PdCategory,
            as: 'pdCategory',
            attributes: ['id', 'name'],
          },
          {
            model: Focus,
            as: 'focus',
            attributes: ['id', 'name'],
            through: { attributes: [] }, // Exclude junction table attributes
          },
          {
            model: WtdCategory,
            as: 'wtdCategories',
            attributes: ['id', 'name'],
            through: { attributes: [] }, // Exclude junction table attributes
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
          {
            model: Admin,
            as: 'reviewer',
            attributes: ['id', 'email'],
          },
        ],
        distinct: true, // Important for correct count with associations
      });

      // Calculate pagination info
      const totalPages = Math.ceil(count / limit);
      const pagination = {
        total: count,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrevious: page > 1,
      };

      return {
        insights: rows,
        pagination,
      };
    } catch (error) {
      console.error('Error in findAllForAdmin repository:', error);
      throw error;
    }
  }

  /**
   * Find insight by ID
   * @param {string} id - Insight UUID
   * @param {string} userId - Optional user ID to check if insight is bookmarked
   * @returns {Promise<Insight>} Insight instance
   * @throws {ApiException} If insight not found
   */
  async findById(id, userId = null) {
    try {
      // Get models
      const PdCategory = databaseService.getPdCategoryModel();
      const Focus = databaseService.getFocusModel();
      const WtdCategory = databaseService.getWtdCategoryModel();
      const Admin = databaseService.getAdminModel();
      const User = databaseService.getUserModel();

      const Insight = databaseService.getInsightModel();
      const insight = await Insight.findByPk(id, {
        attributes: {
          include: [
            // Add subquery for likes count
            [
              databaseService
                .getSequelize()
                .literal(
                  `(SELECT COUNT(*)::INTEGER FROM "LikedInsight" WHERE "LikedInsight"."insightId" = "Insight"."id")`
                ),
              'likesCount',
            ],
            // Add subquery for implementations count
            [
              databaseService
                .getSequelize()
                .literal(
                  `(SELECT COUNT(*)::INTEGER FROM "ImplementedInsight" WHERE "ImplementedInsight"."insightId" = "Insight"."id")`
                ),
              'implementationCount',
            ],
            // Add subquery for contributions count
            [
              databaseService
                .getSequelize()
                .literal(
                  `(SELECT COUNT(*)::INTEGER FROM "Contribution" WHERE "Contribution"."insightId" = "Insight"."id")`
                ),
              'totalContributions',
            ],
            // Add subquery for bookmarks count
            [
              databaseService
                .getSequelize()
                .literal(
                  `(SELECT COUNT(*)::INTEGER FROM "BookmarkedInsight" WHERE "BookmarkedInsight"."insightId" = "Insight"."id")`
                ),
              'bookmarksCount',
            ],
          ],
        },
        include: [
          {
            model: PdCategory,
            as: 'pdCategory',
            attributes: ['id', 'name'],
          },
          {
            model: Focus,
            as: 'focus',
            attributes: ['id', 'name'],
            through: { attributes: [] }, // Exclude junction table attributes
          },
          {
            model: WtdCategory,
            as: 'wtdCategories',
            attributes: ['id', 'name'],
            through: { attributes: [] }, // Exclude junction table attributes
          },
          {
            model: Admin,
            as: 'reviewer',
            attributes: ['id', 'email'],
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
        ],
      });

      if (!insight) {
        throw new ApiException(404, 'Insight not found');
      }

      // If userId is provided, check if the insight is bookmarked, liked, and implemented
      if (userId) {
        const BookmarkedInsight = databaseService.getBookmarkedInsightModel();
        const LikedInsight = databaseService.getLikedInsightModel();
        const ImplementedInsight = databaseService.getImplementedInsightModel();

        // Check if bookmarked
        const bookmark = await BookmarkedInsight.findOne({
          where: {
            userId,
            insightId: id,
          },
        });

        // Check if liked
        const like = await LikedInsight.findOne({
          where: {
            userId,
            insightId: id,
          },
        });

        // Check if implemented
        const implementation = await ImplementedInsight.findOne({
          where: {
            userId,
            insightId: id,
          },
        });

        const insightData = insight.toJSON();
        insightData.isBookmarked = !!bookmark;
        insightData.isLiked = !!like;
        insightData.isImplemented = !!implementation;
        return insightData;
      }

      return insight;
    } catch (error) {
      console.error('Error in findById repository:', error);
      throw error;
    }
  }

  /**
   * Create a new insight
   * @param {Object} data - Insight data
   * @param {string} data.insightText - The insight text
   * @param {string} data.sourceUrl - Optional source URL
   * @param {string} data.pdCategoryId - PD category ID
   * @param {Array<string>} data.focusIds - Array of focus IDs
   * @param {Array<string>} data.wtdCategoryIds - Array of WTD category IDs
   * @returns {Promise<Insight>} Created insight
   */
  async create(data) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      // Create the insight
      const Insight = databaseService.getInsightModel();
      const insight = await Insight.create(
        {
          insightText: data.insightText,
          sourceUrl: data.sourceUrl,
          pdCategoryId: data.pdCategoryId,
          createdBy: data.createdBy,
          status: data.status || 'PENDING',
        },
        { transaction }
      );

      // Add focus associations if provided
      if (data.focusIds && data.focusIds.length > 0) {
        const InsightFocus = databaseService.getInsightFocusModel();
        const focusEntries = data.focusIds.map((focusId) => ({
          id: uuidv4(),
          insightId: insight.id,
          focusId: focusId,
        }));
        await InsightFocus.bulkCreate(focusEntries, { transaction });
      }

      // Add WTD category associations if provided
      if (data.wtdCategoryIds && data.wtdCategoryIds.length > 0) {
        const InsightWtdCategory = databaseService.getInsightWtdCategoryModel();
        const wtdCategoryEntries = data.wtdCategoryIds.map((wtdCategoryId) => ({
          id: uuidv4(),
          insightId: insight.id,
          wtdCategoryId: wtdCategoryId,
        }));
        await InsightWtdCategory.bulkCreate(wtdCategoryEntries, {
          transaction,
        });
      }

      // Commit transaction
      await transaction.commit();

      // Return the created insight with associations
      return this.findById(insight.id);
    } catch (error) {
      // Rollback transaction on error
      await transaction.rollback();
      console.error('Error in create repository:', error);
      throw error;
    }
  }

  /**
   * Update insight
   * @param {string} id - Insight UUID
   * @param {Object} data - Data to update
   * @returns {Promise<Insight>} Updated insight
   * @throws {ApiException} If insight not found
   */
  async update(id, data) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      const insight = await this.findById(id);

      Object.assign(insight, data);
      await insight.save({ transaction });

      // Update focus associations if provided
      if (data.focusIds) {
        const InsightFocus = databaseService.getInsightFocusModel();
        // Delete existing associations
        await InsightFocus.destroy({
          where: { insightId: insight.id },
          transaction,
        });

        // Create new associations
        if (data.focusIds.length > 0) {
          const focusEntries = data.focusIds.map((focusId) => ({
            id: uuidv4(),
            insightId: insight.id,
            focusId: focusId,
          }));
          await InsightFocus.bulkCreate(focusEntries, { transaction });
        }
      }

      // Update WTD category associations if provided
      if (data.wtdCategoryIds) {
        const InsightWtdCategory = databaseService.getInsightWtdCategoryModel();
        // Delete existing associations
        await InsightWtdCategory.destroy({
          where: { insightId: insight.id },
          transaction,
        });

        // Create new associations
        if (data.wtdCategoryIds.length > 0) {
          const wtdCategoryEntries = data.wtdCategoryIds.map(
            (wtdCategoryId) => ({
              id: uuidv4(),
              insightId: insight.id,
              wtdCategoryId: wtdCategoryId,
            })
          );
          await InsightWtdCategory.bulkCreate(wtdCategoryEntries, {
            transaction,
          });
        }
      }

      // Commit transaction
      await transaction.commit();

      // Return the updated insight with associations
      return this.findById(insight.id);
    } catch (error) {
      // Rollback transaction on error
      await transaction.rollback();
      console.error('Error in update repository:', error);
      throw error;
    }
  }

  /**
   * Delete insight
   * @param {string} id - Insight UUID
   * @returns {Promise<boolean>} True if deleted
   * @throws {ApiException} If insight not found
   */
  async delete(id) {
    try {
      const insight = await this.findById(id);
      await insight.destroy();
      return true;
    } catch (error) {
      console.error('Error in delete repository:', error);
      throw error;
    }
  }

  /**
   * Find trending insights based on contributions and likes
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.userId - User ID to check if insights are bookmarked/liked/implemented
   * @returns {Promise<Object>} Object containing trending insights and pagination info
   */
  async findTrending({ page = 1, limit = 10, userId } = {}) {
    try {
      // Trending score weights - easy to modify
      const TRENDING_WEIGHTS = {
        LIKES: 1.0,
        CONTRIBUTIONS: 1.0,
      };

      // Build where clause - only approved insights
      const whereClause = {
        status: InsightStatus.APPROVED,
      };

      // Calculate offset
      const offset = (page - 1) * limit;

      // Get models
      const User = databaseService.getUserModel();
      const PdCategory = databaseService.getPdCategoryModel();
      const Focus = databaseService.getFocusModel();
      const WtdCategory = databaseService.getWtdCategoryModel();

      // Prepare include array for associations
      const includeArray = [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'profilePic'],
        },
        {
          model: PdCategory,
          as: 'pdCategory',
          attributes: ['id', 'name'],
        },
        {
          model: Focus,
          as: 'focus',
          attributes: ['id', 'name'],
          through: { attributes: [] },
        },
        {
          model: WtdCategory,
          as: 'wtdCategories',
          attributes: ['id', 'name'],
          through: { attributes: [] },
        },
      ];

      // Find insights with pagination and trending score calculation
      const Insight = databaseService.getInsightModel();
      const { count, rows } = await Insight.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        distinct: true,
        // Order by trending score (based on likes and contributions only)
        order: [
          [
            databaseService.getSequelize().literal(`
              (
                (SELECT COUNT(*)::INTEGER FROM "LikedInsight" WHERE "LikedInsight"."insightId" = "Insight"."id") * ${TRENDING_WEIGHTS.LIKES} +
                (SELECT COUNT(*)::INTEGER FROM "Contribution" WHERE "Contribution"."insightId" = "Insight"."id") * ${TRENDING_WEIGHTS.CONTRIBUTIONS}
              )
            `),
            'DESC',
          ],
          ['createdAt', 'DESC'], // Secondary sort by creation date
        ],
        attributes: {
          exclude: [
            'pdCategoryId',
            'createdBy',
            'status',
            'reviewedBy',
            'reviewedAt',
            'rejectionReason',
          ],
          include: [
            // Add subquery for likes count
            [
              databaseService
                .getSequelize()
                .literal(
                  `(SELECT COUNT(*)::INTEGER FROM "LikedInsight" WHERE "LikedInsight"."insightId" = "Insight"."id")`
                ),
              'likesCount',
            ],
            // Add subquery for implementations count
            [
              databaseService
                .getSequelize()
                .literal(
                  `(SELECT COUNT(*)::INTEGER FROM "ImplementedInsight" WHERE "ImplementedInsight"."insightId" = "Insight"."id")`
                ),
              'implementationCount',
            ],
            // Add subquery for contributions count
            [
              databaseService
                .getSequelize()
                .literal(
                  `(SELECT COUNT(*)::INTEGER FROM "Contribution" WHERE "Contribution"."insightId" = "Insight"."id")`
                ),
              'totalContributions',
            ],
            // Add subquery for bookmarks count
            [
              databaseService
                .getSequelize()
                .literal(
                  `(SELECT COUNT(*)::INTEGER FROM "BookmarkedInsight" WHERE "BookmarkedInsight"."insightId" = "Insight"."id")`
                ),
              'bookmarksCount',
            ],
            // Add trending score calculation (likes + contributions only)
            [
              databaseService.getSequelize().literal(`
                  (
                    (SELECT COUNT(*)::INTEGER FROM "LikedInsight" WHERE "LikedInsight"."insightId" = "Insight"."id") * ${TRENDING_WEIGHTS.LIKES} +
                    (SELECT COUNT(*)::INTEGER FROM "Contribution" WHERE "Contribution"."insightId" = "Insight"."id") * ${TRENDING_WEIGHTS.CONTRIBUTIONS}
                  )
                `),
              'trendingScore',
            ],
          ],
        },
        include: includeArray,
        subQuery: false,
      });

      // Calculate pagination info
      const totalPages = Math.ceil(count / limit);
      const hasNext = page < totalPages;
      const hasPrevious = page > 1;

      // Check which insights are bookmarked, liked, and implemented by the user
      const BookmarkedInsight = databaseService.getBookmarkedInsightModel();
      const LikedInsight = databaseService.getLikedInsightModel();
      const ImplementedInsight = databaseService.getImplementedInsightModel();

      const insightIds = rows.map((insight) => insight.id);
      const bookmarks = await BookmarkedInsight.findAll({
        where: {
          userId,
          insightId: {
            [Op.in]: insightIds,
          },
        },
        raw: true,
      });

      const likes = await LikedInsight.findAll({
        where: {
          userId,
          insightId: {
            [Op.in]: insightIds,
          },
        },
        raw: true,
      });

      const implementations = await ImplementedInsight.findAll({
        where: {
          userId,
          insightId: {
            [Op.in]: insightIds,
          },
        },
        raw: true,
      });

      const bookmarkedInsightIds = new Set(bookmarks.map((b) => b.insightId));
      const likedInsightIds = new Set(likes.map((l) => l.insightId));
      const implementedInsightIds = new Set(
        implementations.map((i) => i.insightId)
      );

      const insights = rows.map((insight) => {
        const insightData = insight.toJSON();
        insightData.isBookmarked = bookmarkedInsightIds.has(insight.id);
        insightData.isLiked = likedInsightIds.has(insight.id);
        insightData.isImplemented = implementedInsightIds.has(insight.id);
        return insightData;
      });

      return {
        insights,
        pagination: {
          total: count,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrevious,
        },
      };
    } catch (error) {
      console.error('Error in findTrending repository:', error);
      throw error;
    }
  }
}

module.exports = new InsightRepository();
