/**
 * User Insight Controller
 *
 * Handles insight-related HTTP requests for users
 */
const insightService = require('./services/insight.service');
const bookmarkService = require('./services/bookmark.service');
const likeService = require('./services/like.service');
const implementService = require('./services/implement.service');
const contributionService = require('./services/contribution.service');
const reportService = require('./services/report.service');
const { ApiResponse } = require('@utils/response.utils');
const { INSIGHT, REPORT } = require('@utils/messages.utils');

/**
 * Insight controller
 */
const insightController = {
  /**
   * Create a new insight
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  createInsight: async (req, res, next) => {
    try {
      const insightData = {
        insightText: req.body.insightText,
        sourceUrl: req.body.sourceUrl,
        pdCategoryId: req.body.pdCategoryId,
        focusIds: req.body.focusIds,
        wtdCategoryIds: req.body.wtdCategoryIds,
        createdBy: req.user.id,
        status: 'PENDING', // All new insights start as pending
      };

      const insight = await insightService.createInsight(insightData);

      return ApiResponse.created(res, INSIGHT.CREATED, insight);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get all insights
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getAllInsights: async (req, res, next) => {
    try {
      const {
        page = 1,
        limit = 10,
        search = '',
        focusIds = [],
        pdCategoryIds = [],
        wtdCategoryIds = [],
      } = req.query;

      const userId = req.user ? req.user.id : null;

      const result = await insightService.getAllInsights({
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        search,
        userId,
        focusIds,
        pdCategoryIds,
        wtdCategoryIds,
      });

      return ApiResponse.success(
        res,
        INSIGHT.ALL_RETRIEVED,
        result.insights,
        result.pagination
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get trending insights based on contributions and likes
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getTrendingInsights: async (req, res, next) => {
    try {
      const { page = 1, limit = 10 } = req.query;
      const userId = req.user.id; // Always available from auth middleware

      const result = await insightService.getTrendingInsights({
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        userId,
      });

      return ApiResponse.success(
        res,
        INSIGHT.TRENDING_RETRIEVED,
        result.insights,
        result.pagination
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get insight by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getInsightById: async (req, res, next) => {
    try {
      const { id } = req.params;
      const userId = req.user ? req.user.id : null;
      const insight = await insightService.getInsightById(id, userId);

      return ApiResponse.success(res, INSIGHT.RETRIEVED, insight);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get all bookmarked insights
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getBookmarkedInsights: async (req, res, next) => {
    try {
      const { page = 1, limit = 10 } = req.query;
      const userId = req.user.id;

      const result = await bookmarkService.getBookmarkedInsights(userId, {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
      });

      return ApiResponse.success(
        res,
        INSIGHT.BOOKMARKS_RETRIEVED,
        result.insights,
        result.pagination
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Toggle bookmark status for an insight
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  toggleBookmark: async (req, res, next) => {
    try {
      const { insightId } = req.params;
      const userId = req.user.id;

      const result = await bookmarkService.toggleBookmark(userId, insightId);

      return ApiResponse.success(
        res,
        result.isBookmarked ? INSIGHT.BOOKMARK_ADDED : INSIGHT.BOOKMARK_REMOVED,
        result
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Toggle like status for an insight
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  toggleLike: async (req, res, next) => {
    try {
      const { insightId } = req.params;
      const userId = req.user.id;

      const result = await likeService.toggleLike(userId, insightId);

      return ApiResponse.success(
        res,
        result.isLiked ? INSIGHT.LIKE_ADDED : INSIGHT.LIKE_REMOVED,
        result
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Toggle implement status for an insight
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  toggleImplement: async (req, res, next) => {
    try {
      const { insightId } = req.params;
      const userId = req.user.id;

      const result = await implementService.toggleImplement(userId, insightId);

      return ApiResponse.success(
        res,
        result.isImplemented
          ? INSIGHT.IMPLEMENT_ADDED
          : INSIGHT.IMPLEMENT_REMOVED,
        result
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Add a contribution (comment) to an insight
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  addContribution: async (req, res, next) => {
    try {
      const { insightId } = req.params;
      const { content } = req.body;
      const userId = req.user.id;

      const contribution = await contributionService.addContribution(
        insightId,
        userId,
        content
      );

      return ApiResponse.created(
        res,
        'Contribution added successfully',
        contribution
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get all contributions for an insight
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getContributionsByInsight: async (req, res, next) => {
    try {
      const { insightId } = req.params;
      const {
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'DESC',
      } = req.query;
      const userId = req.user?.id;

      const result = await contributionService.getContributionsByInsight(
        insightId,
        { page, limit, sortBy, sortOrder },
        userId
      );

      return ApiResponse.success(
        res,
        'Contributions retrieved successfully',
        result.contributions,
        result.pagination
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Toggle like/unlike for a contribution
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  toggleContributionLike: async (req, res, next) => {
    try {
      const { contributionId } = req.params;
      const userId = req.user.id;

      const result = await contributionService.toggleContributionLike(
        contributionId,
        userId
      );

      const action = result.isLiked ? 'liked' : 'unliked';
      return ApiResponse.success(
        res,
        `Contribution ${action} successfully`,
        result
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Report an insight
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  reportInsight: async (req, res, next) => {
    try {
      const { insightId } = req.params;
      const { reason } = req.body;
      const userId = req.user.id;

      const report = await reportService.reportInsight(
        insightId,
        userId,
        reason
      );

      return ApiResponse.created(res, REPORT.INSIGHT_REPORTED, report);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Report a contribution
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  reportContribution: async (req, res, next) => {
    try {
      const { contributionId } = req.params;
      const { reason } = req.body;
      const userId = req.user.id;

      const report = await reportService.reportContribution(
        contributionId,
        userId,
        reason
      );

      return ApiResponse.created(res, REPORT.CONTRIBUTION_REPORTED, report);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Delete a contribution (user can only delete their own)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  deleteContribution: async (req, res, next) => {
    try {
      const { contributionId } = req.params;
      const userId = req.user.id;

      await contributionService.deleteContribution(contributionId, userId);

      return ApiResponse.success(res, 'Contribution deleted successfully');
    } catch (error) {
      next(error);
    }
  },
};

module.exports = insightController;
