openapi: 3.0.0
info:
  title: WTD Platform User Experience API
  version: 1.0.0
  description: API endpoints for managing experiences as a user

paths:
  /user/experience:
    post:
      tags:
        - User Experience
      summary: Create Experience
      description: Create a new learning experience with weeks, insights, and media
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateExperienceRequest'
      responses:
        '201':
          description: Experience created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperienceResponse'
        '400':
          description: Bad request - Validation errors
        '401':
          description: Unauthorized - User is not authenticated
          
    get:
      tags:
        - User Experience
      summary: List All Experiences
      description: Get a list of all experiences with pagination and optional search
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SearchParam'
        - name: myExperiences
          in: query
          description: Filter to show only current user's experiences
          required: false
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Successfully retrieved experiences
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperienceListResponse'
        '401':
          description: Unauthorized - User is not authenticated

  /user/experience/{id}:
    get:
      tags:
        - User Experience
      summary: Get Experience by ID
      description: Get a specific experience by its ID with full details
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/ExperienceIdParam'
      responses:
        '200':
          description: Successfully retrieved experience
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperienceResponse'
        '401':
          description: Unauthorized - User is not authenticated
        '404':
          description: Experience not found

# components:
#   securitySchemes:
#     BearerAuth:
#       type: http
#       scheme: bearer
#       bearerFormat: JWT

#   parameters:
#     ExperienceIdParam:
#       name: id
#       in: path
#       required: true
#       description: Experience ID
#       schema:
#         type: string
#         format: uuid

#     PageParam:
#       name: page
#       in: query
#       description: Page number (1-based)
#       required: false
#       schema:
#         type: integer
#         minimum: 1
#         default: 1

#     LimitParam:
#       name: limit
#       in: query
#       description: Number of items per page
#       required: false
#       schema:
#         type: integer
#         minimum: 1
#         maximum: 100
#         default: 10

#     SearchParam:
#       name: search
#       in: query
#       description: Search term for filtering
#       required: false
#       schema:
#         type: string

#   schemas:
#     CreateExperienceRequest:
#       type: object
#       required:
#         - title
#         - experienceLength
#       properties:
#         title:
#           type: string
#           description: Experience title
#           example: "Learning React Framework"
#         shortDescription:
#           type: string
#           description: Brief description of the experience
#           example: "A comprehensive React learning experience"
#         longDescription:
#           type: string
#           description: Detailed description of the experience
#           example: "This experience covers all fundamentals of React development..."
#         experienceLength:
#           type: integer
#           minimum: 1
#           maximum: 52
#           description: Duration in weeks
#           example: 4
#         personalNote:
#           type: string
#           description: Creator's personal notes
#           example: "This was inspired by my teaching experience"
#         pdCategoryIds:
#           type: array
#           items:
#             type: string
#             format: uuid
#           description: Array of PD category IDs
#         wtdCategoryIds:
#           type: array
#           items:
#             type: string
#             format: uuid
#           description: Array of WTD category IDs
#         media:
#           type: array
#           items:
#             $ref: '#/components/schemas/MediaInput'
#           description: Experience-level media
#         weeks:
#           type: array
#           items:
#             $ref: '#/components/schemas/WeekInput'
#           description: Weekly breakdown of the experience

#     MediaInput:
#       type: object
#       required:
#         - type
#         - url
#       properties:
#         type:
#           type: string
#           enum: [IMAGE, VIDEO, DOCUMENT, LINK, AUDIO]
#           description: Type of media
#         url:
#           type: string
#           format: uri
#           description: Media URL
#         title:
#           type: string
#           description: Media title

#     WeekInput:
#       type: object
#       required:
#         - weekNumber
#         - title
#       properties:
#         weekNumber:
#           type: integer
#           minimum: 1
#           description: Week number
#         title:
#           type: string
#           description: Week title
#         weeklyWhy:
#           type: string
#           description: Purpose/rationale for the week
#         media:
#           type: array
#           items:
#             $ref: '#/components/schemas/MediaInput'
#           description: Week-specific media
#         insights:
#           type: array
#           items:
#             $ref: '#/components/schemas/InsightInput'
#           maxItems: 5
#           description: Weekly insights (max 5)

#     InsightInput:
#       type: object
#       required:
#         - text
#       properties:
#         text:
#           type: string
#           description: Insight content
#         sourceUrl:
#           type: string
#           format: uri
#           description: Optional source URL
#         focusIds:
#           type: array
#           items:
#             type: string
#             format: uuid
#           description: Array of focus IDs
#         pdCategoryIds:
#           type: array
#           items:
#             type: string
#             format: uuid
#           description: Array of PD category IDs
#         wtdCategoryIds:
#           type: array
#           items:
#             type: string
#             format: uuid
#           description: Array of WTD category IDs

#     ExperienceResponse:
#       type: object
#       properties:
#         status:
#           type: integer
#           example: 200
#         message:
#           type: string
#           example: "Experience retrieved successfully"
#         data:
#           $ref: '#/components/schemas/Experience'

#     ExperienceListResponse:
#       type: object
#       properties:
#         status:
#           type: integer
#           example: 200
#         message:
#           type: string
#           example: "Experiences retrieved successfully"
#         data:
#           type: array
#           items:
#             $ref: '#/components/schemas/ExperienceSummary'
#         meta:
#           $ref: '#/components/schemas/PaginationMeta'

#     Experience:
#       type: object
#       properties:
#         id:
#           type: string
#           format: uuid
#         title:
#           type: string
#         shortDescription:
#           type: string
#         longDescription:
#           type: string
#         experienceLength:
#           type: integer
#         personalNote:
#           type: string
#         createdBy:
#           type: string
#           format: uuid
#         creator:
#           $ref: '#/components/schemas/UserSummary'
#         media:
#           type: array
#           items:
#             $ref: '#/components/schemas/Media'
#         pdCategories:
#           type: array
#           items:
#             $ref: '#/components/schemas/Category'
#         wtdCategories:
#           type: array
#           items:
#             $ref: '#/components/schemas/Category'
#         weeks:
#           type: array
#           items:
#             $ref: '#/components/schemas/ExperienceWeek'
#         createdAt:
#           type: string
#           format: date-time
#         updatedAt:
#           type: string
#           format: date-time

#     ExperienceSummary:
#       type: object
#       properties:
#         id:
#           type: string
#           format: uuid
#         title:
#           type: string
#         shortDescription:
#           type: string
#         experienceLength:
#           type: integer
#         creator:
#           $ref: '#/components/schemas/UserSummary'
#         media:
#           type: array
#           items:
#             $ref: '#/components/schemas/Media'
#           maxItems: 1
#         createdAt:
#           type: string
#           format: date-time

#     ExperienceWeek:
#       type: object
#       properties:
#         id:
#           type: string
#           format: uuid
#         weekNumber:
#           type: integer
#         title:
#           type: string
#         weeklyWhy:
#           type: string
#         media:
#           type: array
#           items:
#             $ref: '#/components/schemas/Media'
#         insights:
#           type: array
#           items:
#             $ref: '#/components/schemas/Insight'

#     Insight:
#       type: object
#       properties:
#         id:
#           type: string
#           format: uuid
#         order:
#           type: integer
#         text:
#           type: string
#         sourceUrl:
#           type: string
#         focuses:
#           type: array
#           items:
#             $ref: '#/components/schemas/Category'
#         pdCategories:
#           type: array
#           items:
#             $ref: '#/components/schemas/Category'
#         wtdCategories:
#           type: array
#           items:
#             $ref: '#/components/schemas/Category'

#     Media:
#       type: object
#       properties:
#         id:
#           type: string
#           format: uuid
#         type:
#           type: string
#           enum: [IMAGE, VIDEO, DOCUMENT, LINK, AUDIO]
#         url:
#           type: string
#         title:
#           type: string
#         order:
#           type: integer

#     Category:
#       type: object
#       properties:
#         id:
#           type: string
#           format: uuid
#         name:
#           type: string

#     UserSummary:
#       type: object
#       properties:
#         id:
#           type: string
#           format: uuid
#         firstName:
#           type: string
#         lastName:
#           type: string
#         email:
#           type: string

#     PaginationMeta:
#       type: object
#       properties:
#         pagination:
#           type: object
#           properties:
#             currentPage:
#               type: integer
#             totalPages:
#               type: integer
#             totalItems:
#               type: integer
#             itemsPerPage:
#               type: integer
#             hasNextPage:
#               type: boolean
#             hasPrevPage:
#               type: boolean

components:
  schemas:
    CreateExperienceRequest:
      type: object
      required:
        - title
        - experienceLength
      properties:
        title:
          type: string
          description: Experience title
          example: "Learning React Framework"
        shortDescription:
          type: string
          description: Brief description of the experience
          example: "A comprehensive React learning experience"
        longDescription:
          type: string
          description: Detailed description of the experience
          example: "This experience covers all fundamentals of React development, including components, state, and lifecycle methods."
        experienceLength:
          type: integer
          minimum: 1
          maximum: 52
          description: Duration in weeks
          example: 4
        personalNote:
          type: string
          description: Creator's personal notes
          example: "Built for frontend bootcamps"
        pdCategoryIds:
          type: array
          items:
            type: string
            format: uuid
          description: Array of PD category IDs
          example: ["b1d2a23f-0c9c-42f4-b1b2-8fd2e36d0001"]
        wtdCategoryIds:
          type: array
          items:
            type: string
            format: uuid
          description: Array of WTD category IDs
          example: ["f3a5b90a-1f0a-4723-95c9-91b90e400001"]
        media:
          type: array
          items:
            $ref: '#/components/schemas/MediaInput'
          description: Experience-level media
          example:
            - type: "VIDEO"
              url: "https://example.com/intro.mp4"
              title: "Intro to React"
        weeks:
          type: array
          items:
            $ref: '#/components/schemas/WeekInput'
          description: Weekly breakdown of the experience
          example:
            - weekNumber: 1
              title: "JSX & Components"
              weeklyWhy: "To understand UI structure in React"
              media:
                - type: "DOCUMENT"
                  url: "https://example.com/week1-notes.pdf"
                  title: "Week 1 Notes"
              insights:
                - text: "JSX combines HTML with JavaScript logic"
                  sourceUrl: "https://reactjs.org/docs/introducing-jsx.html"
                  focusIds: ["29c7c9cc-0d6f-493f-9f02-5a04f1e99999"]
                  pdCategoryIds: ["b1d2a23f-0c9c-42f4-b1b2-8fd2e36d0001"]
                  wtdCategoryIds: ["f3a5b90a-1f0a-4723-95c9-91b90e400001"]

    MediaInput:
      type: object
      required:
        - type
        - url
      properties:
        type:
          type: string
          enum: [IMAGE, VIDEO, DOCUMENT, LINK, AUDIO]
          description: Type of media
          example: "IMAGE"
        url:
          type: string
          format: uri
          description: Media URL
          example: "https://example.com/image.jpg"
        title:
          type: string
          description: Media title
          example: "Thumbnail Image"

    WeekInput:
      type: object
      required:
        - weekNumber
        - title
      properties:
        weekNumber:
          type: integer
          minimum: 1
          description: Week number
          example: 2
        title:
          type: string
          description: Week title
          example: "State Management"
        weeklyWhy:
          type: string
          description: Purpose/rationale for the week
          example: "To manage data flow in React apps"
        media:
          type: array
          items:
            $ref: '#/components/schemas/MediaInput'
          description: Week-specific media
        insights:
          type: array
          items:
            $ref: '#/components/schemas/InsightInput'
          maxItems: 5
          description: Weekly insights (max 5)

    InsightInput:
      type: object
      required:
        - text
      properties:
        text:
          type: string
          description: Insight content
          example: "State allows dynamic updates to UI"
        sourceUrl:
          type: string
          format: uri
          description: Optional source URL
          example: "https://reactjs.org/docs/state-and-lifecycle.html"
        focusIds:
          type: array
          items:
            type: string
            format: uuid
          description: Array of focus IDs
          example: ["abc123de-0f99-4abc-8df1-12feabcd1234"]
        pdCategoryIds:
          type: array
          items:
            type: string
            format: uuid
          description: Array of PD category IDs
          example: ["b1d2a23f-0c9c-42f4-b1b2-8fd2e36d0001"]
        wtdCategoryIds:
          type: array
          items:
            type: string
            format: uuid
          description: Array of WTD category IDs
          example: ["f3a5b90a-1f0a-4723-95c9-91b90e400001"]
