/**
 * User Insight Service
 *
 * Handles business logic for user insights
 */
const insightRepository = require('@models/repositories/insight.repository');
const pdCategoryRepository = require('@models/repositories/pd-category.repository');
const focusRepository = require('@models/repositories/focus.repository');
const wtdCategoryRepository = require('@models/repositories/wtd-category.repository');
const { ApiException } = require('@utils/exception.utils');
const { INSIGHT } = require('@utils/messages.utils');
const { InsightStatus } = require('@utils/enums.utils');

/**
 * Insight service
 */
const insightService = {
  /**
   * Get all insights with pagination and optional search
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @param {string} options.search - Search term
   * @param {string} options.userId - Optional user ID to filter by creator
   * @param {Array<string>} options.focusIds - Optional array of focus IDs to filter by
   * @param {Array<string>} options.pdCategoryIds - Optional array of PD category IDs to filter by
   * @param {Array<string>} options.wtdCategoryIds - Optional array of WTD category IDs to filter by
   * @returns {Promise<Object>} Insights and pagination info
   */
  getAllInsights: async ({
    page,
    limit,
    search,
    userId,
    focusIds = [],
    pdCategoryIds = [],
    wtdCategoryIds = [],
  }) => {
    try {
      return await insightRepository.findAll({
        page,
        limit,
        search,
        focusIds,
        pdCategoryIds,
        wtdCategoryIds,
        userId,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get insight by ID
   * @param {string} id - Insight ID
   * @param {string} userId - Optional user ID to check if user is the creator
   * @returns {Promise<Object>} Insight data
   */
  getInsightById: async (id, userId) => {
    try {
      const insight = await insightRepository.findById(id, userId);

      // Check if insight is approved or if the user is the creator
      if (
        insight.status === InsightStatus.APPROVED ||
        (userId && insight.createdBy === userId)
      ) {
        const result =
          typeof insight.toJSON === 'function' ? insight.toJSON() : insight;
        delete result.status;
        delete result.pdCategoryId;
        delete result.createdBy;
        delete result.reviewedBy;
        delete result.reviewedAt;
        delete result.rejectionReason;
        delete result.reviewer;

        return result;
      }

      // If not approved and user is not the creator, throw an error
      throw new ApiException(404, INSIGHT.NOT_FOUND);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Create a new insight
   * @param {Object} data - Insight data
   * @returns {Promise<Object>} Created insight
   */
  createInsight: async (data) => {
    try {
      // Validate PD category exists
      await pdCategoryRepository.findById(data.pdCategoryId);

      // Validate focus IDs if provided
      if (data.focusIds && data.focusIds.length > 0) {
        for (const focusId of data.focusIds) {
          await focusRepository.findById(focusId);
        }
      }

      // Validate WTD category IDs if provided
      if (data.wtdCategoryIds && data.wtdCategoryIds.length > 0) {
        for (const categoryId of data.wtdCategoryIds) {
          await wtdCategoryRepository.findById(categoryId);
        }
      }

      // Create insight
      return await insightRepository.create(data);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get trending insights based on contributions and likes
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @param {string} options.userId - User ID for personalization (required from auth)
   * @returns {Promise<Object>} Trending insights with pagination
   */
  getTrendingInsights: async ({ page = 1, limit = 10, userId }) => {
    try {
      return await insightRepository.findTrending({
        page,
        limit,
        userId,
      });
    } catch (error) {
      throw error;
    }
  },
};

module.exports = insightService;
