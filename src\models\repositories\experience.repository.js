/**
 * Experience Repository - Modular Version
 * Handles database operations for Experience model
 */
const { ApiException } = require('@utils/exception.utils');
const { Op } = require('sequelize');
const databaseService = require('@config/database.config');
const { v4: uuidv4 } = require('uuid');

class ExperienceRepository {
  constructor() {
    this.models = {
      Experience: databaseService.getExperienceModel(),
      ExperienceMedia: databaseService.getExperienceMediaModel(),
      ExperiencePdCategory: databaseService.getExperiencePdCategoryModel(),
      ExperienceWtdCategory: databaseService.getExperienceWtdCategoryModel(),
      ExperienceWeek: databaseService.getExperienceWeekModel(),
      ExperienceWeekMedia: databaseService.getExperienceWeekMediaModel(),
      ExperienceWeekInsight: databaseService.getExperienceWeekInsightModel(),
      ExperienceWeekInsightFocus:
        databaseService.getExperienceWeekInsightFocusModel(),
      ExperienceWeekInsightPdCategory:
        databaseService.getExperienceWeekInsightPdCategoryModel(),
      ExperienceWeekInsightWtdCategory:
        databaseService.getExperienceWeekInsightWtdCategoryModel(),
      User: databaseService.getUserModel(),
      PdCategory: databaseService.getPdCategoryModel(),
      WtdCategory: databaseService.getWtdCategoryModel(),
      Focus: databaseService.getFocusModel(),
    };
  }

  async create(data) {
    const transaction = await databaseService.getSequelize().transaction();
    try {
      const experience = await this._createExperience(data, transaction);
      await transaction.commit();
      return await this.findById(experience.id);
    } catch (error) {
      await transaction.rollback();
      console.error('Error in create repository:', error);
      throw error;
    }
  }

  async _createExperience(data, transaction) {
    const {
      title,
      shortDescription,
      longDescription,
      experienceLength,
      personalNote,
      createdBy,
      pdCategoryIds = [],
      wtdCategoryIds = [],
      media = [],
      weeks = [],
    } = data;

    const experience = await this.models.Experience.create(
      {
        title,
        shortDescription,
        longDescription,
        experienceLength,
        personalNote,
        createdBy,
      },
      { transaction }
    );

    await this._createMedia(experience.id, media, transaction);
    await this._createCategories(
      experience.id,
      pdCategoryIds,
      wtdCategoryIds,
      transaction
    );
    await this._createWeeks(experience.id, weeks, transaction);

    return experience;
  }

  async _createMedia(experienceId, media, transaction) {
    if (!media.length) return;

    const mediaData = media.map((item, index) => ({
      id: uuidv4(),
      experienceId,
      type: item.type,
      url: item.url,
      title: item.title,
      order: index + 1,
    }));

    await this.models.ExperienceMedia.bulkCreate(mediaData, { transaction });
  }

  async _createCategories(
    experienceId,
    pdCategoryIds,
    wtdCategoryIds,
    transaction
  ) {
    if (pdCategoryIds.length) {
      // Validate PD Category IDs
      const validPdCategories = await this.models.PdCategory.findAll({
        where: { id: pdCategoryIds },
        attributes: ['id'],
        transaction,
      });

      const validPdIds = validPdCategories.map((cat) => cat.id);
      const invalidPdIds = pdCategoryIds.filter(
        (id) => !validPdIds.includes(id)
      );

      if (invalidPdIds.length > 0) {
        throw new ApiException(
          `Invalid PD Category IDs: ${invalidPdIds.join(', ')}`,
          400
        );
      }

      const pdCategoryData = validPdIds.map((id) => ({
        id: uuidv4(),
        experienceId,
        pdCategoryId: id,
      }));
      await this.models.ExperiencePdCategory.bulkCreate(pdCategoryData, {
        transaction,
      });
    }

    if (wtdCategoryIds.length) {
      // Validate WTD Category IDs
      const validWtdCategories = await this.models.WtdCategory.findAll({
        where: { id: wtdCategoryIds },
        attributes: ['id'],
        transaction,
      });

      const validWtdIds = validWtdCategories.map((cat) => cat.id);
      const invalidWtdIds = wtdCategoryIds.filter(
        (id) => !validWtdIds.includes(id)
      );

      if (invalidWtdIds.length > 0) {
        throw new ApiException(
          `Invalid WTD Category IDs: ${invalidWtdIds.join(', ')}`,
          400
        );
      }

      const wtdCategoryData = validWtdIds.map((id) => ({
        id: uuidv4(),
        experienceId,
        wtdCategoryId: id,
      }));
      await this.models.ExperienceWtdCategory.bulkCreate(wtdCategoryData, {
        transaction,
      });
    }
  }

  async _createWeeks(experienceId, weeks, transaction) {
    for (const week of weeks) {
      const experienceWeek = await this.models.ExperienceWeek.create(
        {
          id: uuidv4(),
          experienceId,
          weekNumber: week.weekNumber,
          title: week.title,
          weeklyWhy: week.weeklyWhy,
        },
        { transaction }
      );

      await this._createWeekMedia(experienceWeek.id, week.media, transaction);
      await this._createInsights(experienceWeek.id, week.insights, transaction);
    }
  }

  async _createWeekMedia(weekId, media, transaction) {
    if (!media?.length) return;

    const weekMediaData = media.map((item, index) => ({
      id: uuidv4(),
      experienceWeekId: weekId,
      type: item.type,
      url: item.url,
      title: item.title,
      order: index + 1,
    }));

    await this.models.ExperienceWeekMedia.bulkCreate(weekMediaData, {
      transaction,
    });
  }

  async _createInsights(weekId, insights, transaction) {
    if (!insights?.length) return;

    for (let i = 0; i < insights.length; i++) {
      const data = insights[i];

      const insight = await this.models.ExperienceWeekInsight.create(
        {
          id: uuidv4(),
          experienceWeekId: weekId,
          order: i + 1,
          text: data.text,
          sourceUrl: data.sourceUrl,
        },
        { transaction }
      );

      await this._createInsightAssociations(insight.id, data, transaction);
    }
  }

  async _createInsightAssociations(insightId, data, transaction) {
    if (data.focusIds?.length) {
      // Validate Focus IDs
      const validFocuses = await this.models.Focus.findAll({
        where: { id: data.focusIds },
        attributes: ['id'],
        transaction,
      });

      const validFocusIds = validFocuses.map((focus) => focus.id);
      const invalidFocusIds = data.focusIds.filter(
        (id) => !validFocusIds.includes(id)
      );

      if (invalidFocusIds.length > 0) {
        throw new ApiException(
          `Invalid Focus IDs: ${invalidFocusIds.join(', ')}`,
          400
        );
      }

      const focusData = validFocusIds.map((id) => ({
        id: uuidv4(),
        experienceWeekInsightId: insightId,
        focusId: id,
      }));
      await this.models.ExperienceWeekInsightFocus.bulkCreate(focusData, {
        transaction,
      });
    }

    if (data.pdCategoryIds?.length) {
      // Validate PD Category IDs
      const validPdCategories = await this.models.PdCategory.findAll({
        where: { id: data.pdCategoryIds },
        attributes: ['id'],
        transaction,
      });

      const validPdIds = validPdCategories.map((cat) => cat.id);
      const invalidPdIds = data.pdCategoryIds.filter(
        (id) => !validPdIds.includes(id)
      );

      if (invalidPdIds.length > 0) {
        throw new ApiException(
          `Invalid PD Category IDs in insight: ${invalidPdIds.join(', ')}`,
          400
        );
      }

      const pdData = validPdIds.map((id) => ({
        id: uuidv4(),
        experienceWeekInsightId: insightId,
        pdCategoryId: id,
      }));
      await this.models.ExperienceWeekInsightPdCategory.bulkCreate(pdData, {
        transaction,
      });
    }

    if (data.wtdCategoryIds?.length) {
      // Validate WTD Category IDs
      const validWtdCategories = await this.models.WtdCategory.findAll({
        where: { id: data.wtdCategoryIds },
        attributes: ['id'],
        transaction,
      });

      const validWtdIds = validWtdCategories.map((cat) => cat.id);
      const invalidWtdIds = data.wtdCategoryIds.filter(
        (id) => !validWtdIds.includes(id)
      );

      if (invalidWtdIds.length > 0) {
        throw new ApiException(
          `Invalid WTD Category IDs in insight: ${invalidWtdIds.join(', ')}`,
          400
        );
      }

      const wtdData = validWtdIds.map((id) => ({
        id: uuidv4(),
        experienceWeekInsightId: insightId,
        wtdCategoryId: id,
      }));
      await this.models.ExperienceWeekInsightWtdCategory.bulkCreate(wtdData, {
        transaction,
      });
    }
  }

  async findAll({ page = 1, limit = 10, search = '', createdBy = '' } = {}) {
    try {
      const whereClause = {};

      if (search) {
        whereClause[Op.or] = [
          { title: { [Op.iLike]: `%${search}%` } },
          { shortDescription: { [Op.iLike]: `%${search}%` } },
        ];
      }

      if (createdBy) whereClause.createdBy = createdBy;

      const offset = (page - 1) * limit;

      const { Experience, User, ExperienceMedia, PdCategory, WtdCategory } =
        this.models;

      const { count, rows } = await Experience.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
          { model: ExperienceMedia, as: 'media', required: false },
          {
            model: PdCategory,
            as: 'pdCategories',
            through: { attributes: [] },
            required: false,
          },
          {
            model: WtdCategory,
            as: 'wtdCategories',
            through: { attributes: [] },
            required: false,
          },
        ],
        limit,
        offset,
        order: [['createdAt', 'DESC']],
        distinct: true,
      });

      return {
        experiences: rows,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: limit,
          hasNextPage: page < Math.ceil(count / limit),
          hasPrevPage: page > 1,
        },
      };
    } catch (error) {
      console.error('Error in findAll repository:', error);
      throw error;
    }
  }

  async findById(id, includeWeeks = true) {
    try {
      const {
        Experience,
        User,
        ExperienceMedia,
        PdCategory,
        WtdCategory,
        ExperienceWeek,
        ExperienceWeekMedia,
        ExperienceWeekInsight,
        Focus,
      } = this.models;

      const includeOptions = [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        { model: ExperienceMedia, as: 'media', required: false },
        {
          model: PdCategory,
          as: 'pdCategories',
          through: { attributes: [] },
          required: false,
        },
        {
          model: WtdCategory,
          as: 'wtdCategories',
          through: { attributes: [] },
          required: false,
        },
      ];

      if (includeWeeks) {
        includeOptions.push({
          model: ExperienceWeek,
          as: 'weeks',
          required: false,
          include: [
            { model: ExperienceWeekMedia, as: 'media', required: false },
            {
              model: ExperienceWeekInsight,
              as: 'insights',
              required: false,
              include: [
                { model: Focus, as: 'focuses', through: { attributes: [] } },
                {
                  model: PdCategory,
                  as: 'pdCategories',
                  through: { attributes: [] },
                },
                {
                  model: WtdCategory,
                  as: 'wtdCategories',
                  through: { attributes: [] },
                },
              ],
            },
          ],
        });
      }

      const experience = await Experience.findByPk(id, {
        include: includeOptions,
      });

      if (!experience) {
        throw new ApiException('Experience not found', 404);
      }

      return experience;
    } catch (error) {
      console.error('Error in findById repository:', error);
      throw error;
    }
  }
}

module.exports = new ExperienceRepository();
